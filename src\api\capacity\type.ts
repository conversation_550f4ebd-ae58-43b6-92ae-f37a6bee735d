export interface CapacityReportDto {

  workDate: string

  /**
   * @description The hour of the day (0-23).
   */
  hour: number

  /**
   * @description The planned production quantity for the hour.
   */
  plannedQuantity: number

  /**
   * @description The actual production quantity for the hour.
   */
  actualQuantity: number

  /**
   * @description The achievement rate as a percentage string (e.g., "95%").
   */
  achievementRate: string

  /**
   * @description The status of the production for the hour.
   * '达标' means "Met".
   * '未达标' means "Not Met".
   */
  status: '达标' | '未达标'

  /**
   * @description The reason for not meeting the target, if applicable.
   */
  reason: string | null
}

export interface CapacityReportWithLine {
  lineCode: string
  capacityReportDtos: CapacityReportDto[]
}

export interface PlannedCapacity {
  id: string
  lineId: string
  workDate: string
  hourOfDay: number
  plannedQuantity: number
}

export interface PlannedCapacityCreate {
  lineId: string
  workDate: string
  hourOfDay: number
  plannedQuantity: number
}

export interface PlannedCapacityEdit {
  lineId: string
  workDate: string
  hourOfDay: number
  plannedQuantity: number
}

export interface PlannedCapacitySearch {
  lineId?: string
  workDate?: string
}

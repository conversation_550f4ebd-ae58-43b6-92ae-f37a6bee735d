import type { PageList, Pageable } from '../common/type'
import type { CapacityReportWithLine, PlannedCapacity, PlannedCapacityCreate, PlannedCapacityEdit, PlannedCapacitySearch } from './type'
import type {
  CapacityQueryParams,
  MultiLineCapacityData,
  MultiLineCapacityStatistics,
} from './types'
import { kyBatchDelete, kyDelete, kyGet, kyPost, kyPostFile, kyPut } from '~/utils/request'

export const capacityApi = {
  page: (param: Pageable<Partial<PlannedCapacitySearch>>) => kyPost('planned-capacities/page', param).json<PageList<PlannedCapacity>>(),
  create: (param: PlannedCapacityCreate) => kyPost('planned-capacities', param),
  update: (id: string, param: PlannedCapacityEdit) => kyPut(`planned-capacities/${id}`, param),
  delete: (id: string) => kyDelete(`planned-capacities/${id}`),
  batchDelete: (ids: string[]) => kyBatchDelete('planned-capacities/batch', { json: ids }),
  get: (id: string) => kyGet(`planned-capacities/${id}`).json<PlannedCapacity>(),
  import: (formData: FormData) => kyPostFile('planned-capacities/import', formData),
  downloadTemplate: () => kyGet('planned-capacities/template'),
  export: (param?: Partial<PlannedCapacitySearch>) => kyPost('planned-capacities/export', param),

  /**
   * Get capacity report data by date
   * @param workDate Work date in YYYY-MM-DD format
   * @returns List of capacity reports with line information
   */
  getReport: (workDate: string) => kyGet('capacity/report', { workDate }).json<CapacityReportWithLine[]>(),
  exportReport: (workDate: string) => kyGet('capacity/export', { workDate }),

  // 获取多线产能数据
  getMultiLineCapacityData: (params: CapacityQueryParams) =>
    kyPost('capacity/multi-line/data', params).json<MultiLineCapacityData>(),

  // 获取多线产能统计
  getMultiLineCapacityStatistics: (params: CapacityQueryParams) =>
    kyPost('capacity/multi-line/statistics', params).json<MultiLineCapacityStatistics>(),

  // 获取可用产线列表
  getAvailableLines: () =>
    kyGet('capacity/lines').json<Array<{ lineId: string, lineName: string }>>(),
}

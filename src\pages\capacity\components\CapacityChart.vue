<script setup lang="ts">
import { useDark } from '@vueuse/core'
import { computed } from 'vue'
import type { CapacityReportDto } from '~/api/capacity/type'
import { responsivePx } from '~/utils/responsive'

const props = defineProps<{
  lineId: string
  data: CapacityReportDto[]
}>()

const isDark = useDark()

// 生成图表配置
const chartOption = computed(() => {
  const textColor = isDark.value ? '#E5EAF3' : '#333'
  const splitLineColor = isDark.value ? 'rgba(255, 255, 255, 0.1)' : '#E5E5E5'
  const axisLineColor = isDark.value ? '#8B93A6' : '#BDBDBD'

  const labels = props.data.map(d => `${d.hour}:00 - ${(d.hour + 1) % 24}:00`)
  const plannedData = props.data.map(d => d.plannedQuantity)
  const actualData = props.data.map(d => d.actualQuantity)

  const hasData = props.data.length > 0

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: { type: 'cross' },
      backgroundColor: isDark.value ? 'rgba(30, 30, 30, 0.95)' : 'rgba(255, 255, 255, 0.95)',
      borderColor: isDark.value ? '#4C515C' : '#E0E0E0',
      borderWidth: 1,
      textStyle: { color: textColor, fontSize: responsivePx(14) },
      padding: [10, 15],
      extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);',
    },
    legend: {
      data: ['计划产能', '实际产能'],
      textStyle: { color: textColor, fontSize: responsivePx(14) },
      top: '5%',
      itemGap: 20,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '8%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLine: { lineStyle: { color: axisLineColor } },
      axisLabel: {
        color: textColor,
        fontSize: responsivePx(12),
        rotate: 0,
      },
      splitLine: { show: false },
      axisTick: { show: false },
    },
    yAxis: {
      type: 'value',
      name: '产能',
      nameTextStyle: {
        color: textColor,
        fontSize: responsivePx(14),
        padding: [0, 0, 0, 30],
      },
      axisLine: { show: false },
      axisTick: { show: false },
      axisLabel: {
        color: textColor,
        fontSize: responsivePx(12),
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: splitLineColor,
        },
      },
      min: 0,
    },
    series: [
      {
        name: '计划产能',
        type: 'line',
        data: plannedData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          color: '#3B82F6',
          width: 3,
          type: 'dashed',
          shadowColor: 'rgba(59, 130, 246, 0.3)',
          shadowBlur: 10,
        },
        itemStyle: {
          color: '#3B82F6',
          borderWidth: 2,
          borderColor: isDark.value ? '#1E1E1E' : '#fff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(59, 130, 246, 0.4)' },
              { offset: 1, color: 'rgba(59, 130, 246, 0.05)' },
            ],
          },
        },
      },
      {
        name: '实际产能',
        type: 'line',
        data: actualData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          color: '#10B981',
          width: 3,
          shadowColor: 'rgba(16, 185, 129, 0.3)',
          shadowBlur: 10,
        },
        itemStyle: {
          color: '#10B981',
          borderWidth: 2,
          borderColor: isDark.value ? '#1E1E1E' : '#fff',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(16, 185, 129, 0.4)' },
              { offset: 1, color: 'rgba(16, 185, 129, 0.05)' },
            ],
          },
        },
      },
    ],
    graphic: !hasData
      ? [{
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无数据',
            fontSize: responsivePx(16),
            fontWeight: 'bold',
            fill: textColor,
          },
        }]
      : undefined,
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicOut',
  }
})
</script>

<template>
  <div class="h-full w-full">
    <e-charts
      :option="chartOption"
      autoresize
      class="h-full w-full"
    />
  </div>
</template>

<style scoped>
/* 确保图表容器有正确的尺寸 */
.w-full.h-full {
  min-height: 300px;
}
</style>

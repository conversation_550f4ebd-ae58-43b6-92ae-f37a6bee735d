<script setup lang="ts">
import { useRouter } from 'vue-router'
import DeviceCtCard from './DeviceCtCard.vue'
import DualTrackDeviceCard from './DualTrackDeviceCard.vue'
import { useAnalyticsSearchSchema } from './schema'
import ChartDefectType from './ChartDefectType.vue'
import ChartHourlyOutput from './ChartHourlyOutput.vue'
import ChartTop5Defects from './ChartTop5Defects.vue'
import { analyzeApi } from '~/api/analyze'
import type { AlarmInfo, DefectTypeResult, DeviceCtInfo, HourlyOutputList, OeeResult } from '~/api/analyze/type'
import { TriggerRecordApi } from '~/api/feedback/trigger'
import { useDashboardStore } from '~/stores/dashboard'

const props = defineProps<{
  code: string
}>()

const dashboardStore = useDashboardStore()
const form = useAnalyticsSearchSchema()
const loading = ref<boolean>(false)

const oee = ref<OeeResult>()
const devicesCt = ref<DeviceCtInfo[]>()
const hourlyOutput = ref<HourlyOutputList>()
const firstPassDefectTypes = ref<DefectTypeResult>()
const retrialDefectTypes = ref<DefectTypeResult>()
const topAlarms = ref<AlarmInfo[]>()
const productionLineType = ref<string>('')
const refreshInterval = ref<ReturnType<typeof setInterval>>()

// 计算属性
const deviceChunks = computed(() => {
  const devices = devicesCt.value ?? []
  const chunkSize = 5
  return Array.from({ length: Math.ceil(devices.length / chunkSize) }, (_, i) => devices.slice(i * chunkSize, (i + 1) * chunkSize))
})

const devicesByTrack = computed(() => {
  const grouped = new Map<string, DeviceCtInfo[]>()
  devicesCt.value?.forEach((device) => {
    if (!grouped.has(device.trackName)) {
      grouped.set(device.trackName, [])
    }
    grouped.get(device.trackName)?.push(device)
  })
  return grouped
})

function calculateShiftTimes(now: Date) {
  const currentDay = new Date(now)
  const nextDay = new Date(now)
  nextDay.setDate(now.getDate() + 1)

  const startTime = new Date(currentDay.setHours(8, 0, 0, 0))
  const endTime = new Date(nextDay.setHours(8, 0, 0, 0))
  return { startTime, endTime }
}

async function initializeDates(currentTime?: Date) {
  let now: Date
  if (currentTime) {
    now = currentTime
  }
  else {
    try {
      const serverTime = await analyzeApi.getServerTime()
      now = new Date(serverTime)
    }
    catch (error) {
      console.error('Failed to get server time, falling back to local time:', error)
      now = new Date()
    }
  }

  const { startTime, endTime } = calculateShiftTimes(now)
  setStartTime(startTime)
  setEndTime(endTime)
}

async function checkAndUpdateDates() {
  try {
    const serverTime = await analyzeApi.getServerTime()
    const now = new Date(serverTime)
    const endTime = form.values.endTime
    if (!endTime || now > endTime) {
      await initializeDates(now)
    }
  }
  catch (error) {
    console.error('Failed to get server time for checkAndUpdateDates:', error)
  }
}

async function safeApiCall(apiFunction: () => Promise<any>, setter: (data: any) => void) {
  try {
    const result = await apiFunction()
    setter(result)
  }
  catch (error) {
    console.error('API call failed:', error)
  }
}

const search = form.handleSubmit(async (values) => {
  loading.value = true

  await Promise.allSettled([
    safeApiCall(() => analyzeApi.getLineOee(values), (data) => {
      oee.value = data.oeeResult
      devicesCt.value = data.deviceCtInfos
      firstPassDefectTypes.value = data.firstPassDefectTypes
      retrialDefectTypes.value = data.retrialDefectTypes
      dashboardStore.setCurrentProduct(data.productionLineResult.productionLineInfos)
      productionLineType.value = data.productionLineResult.productionLineType
    }),
    safeApiCall(() => analyzeApi.getHourlyOutput(values), data => hourlyOutput.value = data),
    safeApiCall(() => analyzeApi.getTopAlarms(values), data => topAlarms.value = data),
    safeApiCall(() => TriggerRecordApi.latestOpenException(values.code), (data) => {
      dashboardStore.setException(data.anomaliesName)
    }),
    safeApiCall(() => analyzeApi.getServerTime(), data => dashboardStore.setCurrentTime(
      new Date(data).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      }).replace(/\//g, '-'),
    )),
  ])

  loading.value = false
})

// 生命周期和监听器
watch(() => props.code, async (newCode) => {
  if (newCode) {
    // 设置产品线
    form.setFieldValue('code', newCode)
    // 初始化日期
    await initializeDates()
    // 搜索
    await search()
  }
}, { immediate: true })

onBeforeMount(() => {
  refreshInterval.value = setInterval(() => {
    checkAndUpdateDates()
    search()
  }, 300000) // 5分钟刷新一次
})

onBeforeUnmount(() => {
  // 清除自动刷新
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
  }
})

// 设置开始时间
function setStartTime(value: Date) {
  form.setFieldValue('startTime', value)
}

// 设置结束时间
function setEndTime(value: Date) {
  form.setFieldValue('endTime', value)
}

// 重置时间
function resetDates() {
  initializeDates()
  search()
}

// 跳转到产能报表页
function navigateToCapacity() {
  const router = useRouter()
  router.push('/layout/capacity/multi-line-dashboard')
}
</script>

<template>
  <div class="box-border h-full w-full p-1 light:bg-gray-100" :class="{ light: !isDark, dark: isDark }">
    <form v-show="dashboardStore.isSearchVisible" class="mb-2 flex flex-none justify-end gap-4 px-8" @submit="search">
      <FDatePicker name="startTime" label="开始时间" :date-props="{ showTime: true, hourFormat: '24' }" />
      <FDatePicker name="endTime" label="结束时间" :date-props="{ showTime: true, hourFormat: '24' }" />
      <Button class="mt-6" size="small" icon="pi pi-search" type="submit" :loading="loading" />
      <Button class="mt-6" size="small" icon="pi pi-refresh" @click="resetDates" />
    </form>

    <div class="grid grid-rows-14 h-full w-full gap-1">
      <div class="grid row-span-2 grid-cols-24 items-center justify-items-center gap-1">
        <div
          class="col-span-3 h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>开班时间</span>
          <span class="text-2xl text-blue-500 font-600 lg:text-4xl">{{ oee?.actualPlanTime }} h</span>
        </div>
        <div
          class="col-span-3 h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>运机时间</span>
          <span class="text-2xl text-green-500 font-600 lg:text-4xl">{{ oee?.runTime }} h</span>
        </div>
        <div
          class="col-span-3 h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>停机时间</span>
          <span class="text-2xl text-red-500 font-600 lg:text-4xl">{{ oee?.stopTime }} h</span>
        </div>
        <div
          class="col-span-4 h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>标准生产数量 | 实际生产数量</span>
          <span class="text-2xl text-purple-500 font-600 lg:text-4xl">{{ oee?.planBoard }} | {{ oee?.actualBoard }}</span>
        </div>
        <div
          class="col-span-5 h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>标准点位数量 | 实际点位数量</span>
          <span class="text-2xl text-purple-500 font-600 lg:text-4xl">{{ oee?.planBoardPoints }} | {{ oee?.actualBoardPoints
          }}</span>
        </div>
        <div
          class="col-span-3 h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>不良品数：直通 | 复判</span>
          <span class="text-2xl text-orange-500 font-600 lg:text-4xl">{{ oee?.firstPassDefectCount || 0 }} | {{ oee?.defectCount
            || 0 }}</span>
        </div>
        <div
          class="col-span-3 h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>换线次数 | 换线时间</span>
          <span class="text-2xl text-yellow-500 font-600 lg:text-4xl">{{ oee?.changeoverNum || 0 }} | {{ oee?.changeoverTime
          }}</span>
        </div>
      </div>

      <div class="grid row-span-2 grid-cols-4 items-center justify-items-center gap-1">
        <div
          class="h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>目标运转率 | 实际运转率</span>
          <span class="text-2xl text-blue-500 font-600 lg:text-4xl">{{ oee?.availabilityTarget }}% | {{ oee?.availability }}%</span>
        </div>
        <div
          class="h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>目标有效生产率 | 实际有效生产率</span>
          <span class="text-2xl text-purple-500 font-600 lg:text-4xl">{{ oee?.performanceTarget }}% | {{ oee?.performance }}%</span>
        </div>
        <div
          class="h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>目标良品率 | 实际良品率</span>
          <span class="text-2xl text-green-500 font-600 lg:text-4xl">{{ oee?.qualityTarget }}% | {{ oee?.quality }}%</span>
        </div>
        <div
          class="h-full w-full flex flex-col items-center justify-center gap-1 py-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <span>目标OEE | 实际OEE</span>
          <span class="text-2xl text-orange-500 font-600 lg:text-4xl">{{ oee?.oeeTarget }}% | {{ oee?.oee }}%</span>
        </div>
      </div>

      <div class="grid row-span-5 grid-cols-10 gap-1">
        <!-- 设备ct 单轨 -->
        <div
          v-if="productionLineType === 'singleTrack'"
          class="col-span-4 min-h-0 flex flex-col p-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <h5>
            设备CT
          </h5>
          <div class="h-full">
            <template v-if="deviceChunks.length">
              <Carousel
                :value="deviceChunks" :num-visible="1" :num-scroll="1" :circular="false" :pt="{
                  root: { class: 'h-full' },
                  contentContainer: { class: 'h-full' },
                  content: { class: 'h-full' },
                  viewport: { class: 'h-full' },
                  itemList: { class: 'h-full' },
                  item: { class: 'h-full' },
                }"
              >
                <template #item="{ data }">
                  <div class="grid grid-cols-5 h-full min-w-0 w-full place-items-center">
                    <DeviceCtCard v-for="device in data" :key="device.code" :device="device" />
                  </div>
                </template>
              </Carousel>
            </template>
            <template v-else>
              <div class="h-full flex items-center justify-center">
                <span>暂无设备</span>
              </div>
            </template>
          </div>
        </div>

        <!-- 设备ct 双轨 -->
        <div v-if="productionLineType === 'dualTrack'" class="col-span-4 min-h-0 flex flex-col p-4 panel">
          <h5 class="mb-2 flex-none">
            设备CT
          </h5>
          <div class="h-full flex flex-1 flex-col overflow-hidden">
            <div v-for="[trackName, devices] in devicesByTrack" :key="trackName" class="h-1/2 flex items-center">
              <div class="flex-none text-primary font-600">
                {{ trackName }}
              </div>
              <div class="flex flex-1 gap-1 overflow-x-auto">
                <DualTrackDeviceCard v-for="device in devices" :key="device.code" :device="device" />
              </div>
            </div>
          </div>
        </div>

        <div
          class="col-span-3 min-h-0 flex flex-col p-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <h5>
            不良类型(直通)
          </h5>
          <div class="h-full">
            <ChartDefectType v-if="firstPassDefectTypes" :data="firstPassDefectTypes" />
          </div>
        </div>

        <div
          class="col-span-3 min-h-0 flex flex-col p-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <h5>
            不良类型（复判）
          </h5>
          <div class="h-full">
            <ChartDefectType v-if="retrialDefectTypes" :data="retrialDefectTypes" />
          </div>
        </div>
      </div>

      <div class="grid row-span-5 grid-cols-10 gap-1">
        <div
          class="col-span-6 min-h-0 flex flex-col cursor-pointer p-4 transition-shadow panel light:border light:border-black/10 light:rounded-lg light:bg-white hover:shadow-lg light:shadow"
          @click="navigateToCapacity"
        >
          <h5 class="flex items-center gap-2">
            每小时产出
            <i class="pi pi-external-link text-sm text-gray-500" />
          </h5>
          <div class="h-full">
            <ChartHourlyOutput :data="hourlyOutput" />
          </div>
        </div>

        <div
          class="col-span-4 min-h-0 flex flex-col p-4 panel light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
        >
          <h5>
            TOP5异常
          </h5>
          <div class="h-full">
            <ChartTop5Defects :data="topAlarms" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

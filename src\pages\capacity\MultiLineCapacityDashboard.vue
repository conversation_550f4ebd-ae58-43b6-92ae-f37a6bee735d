<script setup lang="ts">
import { format } from 'date-fns'
import type { DataTableCellEditCompleteEvent } from 'primevue/datatable'
import CapacityChart from './components/CapacityChart.vue'
import { capacityApi } from '~/api/capacity'
import type { CapacityReportWithLine } from '~/api/capacity/type'
import { unmetReasonApi } from '~/api/unmet-reason'
import type { UnmetReasonCreateParam } from '~/api/unmet-reason/type'
import LazyLoad from '~/components/common/LazyLoad.vue'
import PageContainer from '~/components/common/PageContainer.vue'

// 响应式数据
const selectedDate = ref<Date>(new Date())
const loading = ref(false)
const exportLoading = ref(false)

const lineData = ref<CapacityReportWithLine[]>()

// 刷新数据
async function refreshData() {
  loading.value = true
  try {
    const dateStr = format(selectedDate.value, 'yyyy-MM-dd')
    // 调用实际的API
    const res = await capacityApi.getReport(dateStr)

    lineData.value = res
  }
  catch (error) {
    console.error('获取数据失败:', error)
  }
  finally {
    loading.value = false
  }
}

async function exportData() {
  exportLoading.value = true
  try {
    await handleFileExport(
      () => capacityApi.exportReport(format(selectedDate.value, 'yyyy-MM-dd')),
      '产能报表.xlsx',
    )
  }
  catch (error) {
    console.error('导出过程中发生错误:', error)
  }
  finally {
    exportLoading.value = false
  }
}

async function onCellEditComplete(event: DataTableCellEditCompleteEvent, lineCode: string) {
  const { data, newValue, field } = event
  if (field === 'reason' && newValue) {
    const param: UnmetReasonCreateParam = {
      lineId: lineCode,
      reason: newValue,
      workDate: data.workDate,
      hourOfDay: data.hour,
    }
    await unmetReasonApi.create(param)
    refreshData()
  }
}

// 组件挂载时加载数据
onMounted(() => {
  refreshData()
})
</script>

<template>
  <PageContainer :class="{ light: !isDark, dark: isDark }">
    <!-- 页面标题 -->
    <div class="mb-6 text-center">
      <h1 class="text-2xl text-primary font-bold">
        多线产能看板
      </h1>
      <!-- <p class="mt-2 text-muted-color">
        Multi-Line Capacity Dashboard
      </p> -->
    </div>

    <!-- 筛选器 -->
    <div class="mb-6 flex items-center justify-center gap-4 border border-surface-border rounded-lg bg-surface-800 p-4 light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow">
      <label class="font-medium">日期:</label>
      <DatePicker
        v-model="selectedDate"
        date-format="yy-mm-dd"
        show-icon
        fluid
        class="w-48"
      />
      <Button
        label="刷新全部"
        icon="pi pi-refresh"
        :loading="loading"
        @click="refreshData"
      />

      <Button
        label="导出"
        icon="pi pi-download"
        :loading="exportLoading"
        @click="exportData"
      />
    </div>

    <!-- 加载指示器 -->
    <div v-if="loading" class="flex items-center justify-center py-10">
      <ProgressSpinner />
    </div>

    <!-- 产线报表容器 -->
    <div v-else-if="lineData && lineData.length > 0" class="space-y-8">
      <div
        v-for="lineReport in lineData"
        :key="lineReport.lineCode"
        class="border border-surface-border rounded-lg bg-surface-800 p-6 shadow-sm light:border light:border-black/10 light:rounded-lg light:bg-white light:shadow"
      >
        <!-- 产线标题 -->
        <h2 class="mb-6 border-b border-surface-border pb-3 text-xl text-primary font-semibold">
          产线: {{ lineReport.lineCode }}
        </h2>

        <LazyLoad min-height="600px">
          <!-- 图表 -->
          <div class="mb-6 h-80">
            <CapacityChart
              :line-id="lineReport.lineCode"
              :data="lineReport.capacityReportDtos"
            />
          </div>

          <!-- 数据表格 -->
          <DataTable
            :value="lineReport.capacityReportDtos"
            class="mt-4"
            edit-mode="cell"
            responsive-layout="scroll"
            :virtual-scroller-options="{ itemSize: 46 }"
            scrollable
            striped-rows scroll-height="400px"
            @cell-edit-complete="(event) => onCellEditComplete(event, lineReport.lineCode)"
          >
            <Column field="workDate" header="工作日期" />
            <Column field="hour" header="小时 (h)">
              <template #body="{ data }">
                {{ data.hour }}:00-{{ (data.hour + 1) % 24 }}:00
              </template>
            </Column>
            <Column field="plannedQuantity" header="计划产能 (pcs)" />
            <Column field="actualQuantity" header="实际产能 (pcs)" />
            <Column field="achievementRate" header="达成率 (%)" />
            <Column field="status" header="状态">
              <template #body="{ data }">
                <Tag
                  :value="data.status"
                  :severity="data.status === '达成' ? 'success' : 'danger'"
                />
              </template>
            </Column>
            <Column field="reason" header="未达成原因">
              <template #body="{ data }">
                {{ data.reason }}
              </template>
              <template #editor="{ data, field }">
                <InputText v-model="data[field]" fluid autofocus />
              </template>
            </Column>
          </DataTable>
        </LazyLoad>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-else class="py-10 text-center">
      <p>没有可显示的数据。</p>
    </div>
  </PageContainer>
</template>
